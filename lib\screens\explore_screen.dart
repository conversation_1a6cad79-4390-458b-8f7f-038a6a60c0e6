import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:wicker/services/config_service.dart'; // Import ConfigService
import 'package:wicker/services/places_service.dart';
import 'package:wicker/widgets/custom_map_marker.dart';
import 'package:wicker/widgets/media_player.dart';
import 'package:wicker/widgets/place_detail_card.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wicker/screens/detail_scroll_viewer.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:geolocator/geolocator.dart';
import 'package:wicker/services/explore_service.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  _ExploreScreenState createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final PlacesService _placesService = PlacesService();
  final ConfigService _configService = ConfigService.instance;
  late Future<List<Map<String, dynamic>>> _contentFuture;
  late Future<String> _baseUrlFuture;
  final MapController _mapController = MapController(); // Add a MapController
  final ExploreService _exploreService =
      ExploreService(); // Add the new service
  List<Map<String, dynamic>> _searchResults = [];
  bool _isSearching = false;

  bool _isMapView = true;
  Map<String, dynamic>? _selectedPlace;
  String _activeFilter = 'All';

  final List<Map<String, dynamic>> _categories = [
    {'name': 'All', 'icon': Icons.public, 'color': const Color(0xFF6C5CE7)},
    {
      'name': 'Restaurant',
      'icon': Icons.restaurant_menu,
      'color': const Color(0xFFFF6B6B),
    },
    {
      'name': 'Cafe',
      'icon': Icons.local_cafe,
      'color': const Color(0xFF4ECDC4),
    },
    {'name': 'Shop', 'icon': Icons.store, 'color': const Color(0xFF00D2D3)},
    {'name': 'Art', 'icon': Icons.palette, 'color': const Color(0xFFFFE66D)},
  ];

  // --- NEW: Function to get user location and center the map ---
  Future<void> _centerOnUserLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Handle case where location services are disabled
      return;
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Handle case where permission is denied
        return;
      }
    }
    Position position = await Geolocator.getCurrentPosition();
    _mapController.move(LatLng(position.latitude, position.longitude), 14.0);
  }

  // --- NEW: Function to handle search queries ---
  Future<void> _onSearchChanged(String query) async {
    if (query.isEmpty) {
      setState(() => _searchResults = []);
      return;
    }
    setState(() => _isSearching = true);
    try {
      final results = await _exploreService.searchBusinesses(query);
      setState(() => _searchResults = results);
    } catch (e) {
      // Handle search error
    } finally {
      setState(() => _isSearching = false);
    }
  }




  

  @override
  void initState() {
    super.initState();
    // Fetch both the content and the server configuration at the same time
    _contentFuture = _placesService.getPlaces();
    _baseUrlFuture = _configService.getBaseUrl();
  }

  /// Builds map markers, applying the active category filter.
  List<Marker> _buildMarkers(List<Map<String, dynamic>> places) {
    return places
        .where(
          (item) =>
              item['content_type'] == 'place' &&
              item['location'] != null &&
              (_activeFilter == 'All' || item['category'] == _activeFilter),
        )
        .map((place) {
          final coords = place['location']['coordinates'];
          return Marker(
            width: 80.0,
            height: 80.0,
            point: LatLng(coords[1], coords[0]),
            child: GestureDetector(
              onTap: () => setState(() => _selectedPlace = place),
              child: CustomMapMarker(category: place['category']),
            ),
          );
        })
        .toList();
  }

  /// Takes the raw list of content and correctly groups it by category,
  /// ensuring all necessary data like authorName is preserved.
  Map<String, List<Map<String, dynamic>>> _prepareDataForViewer(
    List<Map<String, dynamic>> allContent,
  ) {
    final Map<String, List<Map<String, dynamic>>> grouped = {};

    for (var item in allContent) {
      final contentType = item['content_type'] ?? 'post';
      final category = contentType == 'place'
          ? item['category'] as String? ?? 'Other'
          : 'Posts';

      Map<String, dynamic> processedItem = Map.from(item);

      // This is the crucial step: extract the author's name
      final authorDetails =
          item['author_details'] as Map<String, dynamic>? ?? {};
      processedItem['authorName'] = authorDetails['username'] ?? 'Unknown User';

      if (grouped.containsKey(category)) {
        grouped[category]!.add(processedItem);
      } else {
        grouped[category] = [processedItem];
      }
    }
    return grouped;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title: const Text(
            'Explore Accra',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: IconButton(
                icon: Icon(
                  _isMapView ? EvaIcons.grid : EvaIcons.map,
                  color: Colors.black,
                ),
                onPressed: () => setState(() => _isMapView = !_isMapView),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: FutureBuilder<List<dynamic>>(
        future: Future.wait([_contentFuture, _baseUrlFuture]),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError ||
              !snapshot.hasData ||
              snapshot.data!.isEmpty) {
            return Center(
              child: Text(
                'Error: ${snapshot.error ?? "Could not load content."}',
              ),
            );
          }

          final allContent = snapshot.data![0] as List<Map<String, dynamic>>;
          final baseUrl = snapshot.data![1] as String;
          final markers = _buildMarkers(allContent);
          final groupedAndProcessedData = _prepareDataForViewer(allContent);

          return _isMapView
              ? _buildInteractiveMapView(markers)
              : _buildDiscoveryGrid(
                  allContent,
                  groupedAndProcessedData,
                  baseUrl,
                );
        },
      ),

      // --- NEW: Display search results as an overlay ---
      if (_searchResults.isNotEmpty)
        Positioned.fill(
          child: Container(
            color: Colors.white,
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final business = _searchResults[index];
                return ListTile(
                  title: Text(business['business_name']),
                  subtitle: Text(business['description']),
                  onTap: () {
                    // TODO: Navigate to the business's detail page
                  },
                );
              },
            ),
          ),
        ),

    );
  }

  /// Builds the interactive map view with filter chips correctly positioned on top.
  Widget _buildInteractiveMapView(List<Marker> markers) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(4.0), // Minimal padding for a wider map
          child: NeuCard(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(13),
              child: Stack(
                children: [
                  FlutterMap(
                    options: MapOptions(
                      initialCenter: const LatLng(5.6037, -0.1870),
                      initialZoom: 12.0,
                      onTap: (_, __) => setState(() => _selectedPlace = null),
                    ),
                    children: [
                      TileLayer(
                        urlTemplate:
                            'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
                        subdomains: const ['a', 'b', 'c', 'd'],
                      ),
                      MarkerLayer(markers: markers),
                    ],
                  ),
                  _buildFilterChips(), // Chips are now inside the map's Stack
                ],
              ),
            ),
          ),
        ),
                // --- NEW: "Center on Me" Button ---
        Positioned(
          bottom: 32,
          right: 16,
          child: GestureDetector(
            onTap: _centerOnUserLocation,
            child: const NeuCard(
              padding: EdgeInsets.all(12),
              child: Icon(Icons.my_location),
            ),
          ),
        ),
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          bottom: _selectedPlace == null ? -250 : 0,
          left: 0,
          right: 0,
          child: PlaceDetailCard(
            placeData: _selectedPlace ?? {},
            onClose: () => setState(() => _selectedPlace = null),
          ),
        ),
      ],
    );
  }

  /// Builds the filter chips as a positioned overlay.
  Widget _buildFilterChips() {
    return Positioned(
      top: 16,
      left: 0,
      right: 0,
      child: SizedBox(
        height: 50,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final bool isSelected = _activeFilter == category['name'];
            return GestureDetector(
              onTap: () {
                setState(() {
                  _activeFilter = category['name'];
                  _selectedPlace = null;
                });
              },
              child: NeuCard(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                backgroundColor: isSelected ? category['color'] : Colors.white,
                child: Row(
                  children: [
                    Icon(
                      category['icon'],
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      category['name'],
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Builds the masonry grid for discovery view.
  Widget _buildDiscoveryGrid(
    List<Map<String, dynamic>> allContent,
    Map<String, List<Map<String, dynamic>>> groupedData,
    String baseUrl,
  ) {
    return MasonryGridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      padding: const EdgeInsets.all(16.0),
      itemCount: allContent.length,
      itemBuilder: (context, index) {
        final item = allContent[index];
        final contentType = item['content_type'];
        final String title = item['text_content'] ?? item['name'] ?? 'Untitled';
        Map<String, dynamic> mediaDataForPlayer;

        if (contentType == 'post') {
          final List<dynamic> mediaList = item['media'] as List<dynamic>? ?? [];
          if (mediaList.isEmpty) {
            return NeuCard(child: Center(child: Text(title)));
          }
          mediaDataForPlayer = mediaList.first as Map<String, dynamic>;
        } else {
          final List<dynamic> photoList =
              item['photos'] as List<dynamic>? ?? [];
          if (photoList.isEmpty) {
            return NeuCard(child: Center(child: Text(title)));
          }
          mediaDataForPlayer = {
            'path': photoList.first,
            'type': 'image',
            'aspect_ratio': 1.0,
          };
        }

        return GestureDetector(
          onTap: () {
            // FIXED: Determine category consistently with grouping logic
            final category = contentType == 'place'
                ? item['category'] as String? ?? 'Other'
                : 'Posts';

            final categoryList = groupedData[category] ?? [];
            final categoryKeys = groupedData.keys.toList();
            final categoryIndex = categoryKeys.indexOf(category);

            // FIXED: More robust item lookup using a unique identifier
            final itemId = item['_id'];
            final itemIndex = categoryList.indexWhere((listItem) {
              final listItemId = listItem['_id'];

              // Handle different _id formats
              String normalizedItemId = '';
              String normalizedListItemId = '';

              if (itemId is Map && itemId.containsKey('\$oid')) {
                normalizedItemId = itemId['\$oid'].toString();
              } else if (itemId is String) {
                normalizedItemId = itemId;
              } else {
                normalizedItemId = itemId.toString();
              }

              if (listItemId is Map && listItemId.containsKey('\$oid')) {
                normalizedListItemId = listItemId['\$oid'].toString();
              } else if (listItemId is String) {
                normalizedListItemId = listItemId;
              } else {
                normalizedListItemId = listItemId.toString();
              }

              return normalizedItemId == normalizedListItemId;
            });

            print('DEBUG: Tapping item in category: $category');
            print('DEBUG: Item index in category: $itemIndex');
            print('DEBUG: Category index: $categoryIndex');
            print('DEBUG: Available categories: $categoryKeys');

            if (itemIndex != -1 && categoryIndex != -1) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailScrollViewer(
                    allCategoriesData: groupedData,
                    initialCategoryIndex: categoryIndex,
                    initialItemIndex: itemIndex,
                  ),
                ),
              );
            } else {
              // Better error handling
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Unable to open item details. Please try again.',
                  ),
                  duration: Duration(seconds: 2),
                ),
              );

              print("ERROR: Could not find index for tapped item.");
              print(
                "Category: $category, ItemIndex: $itemIndex, CategoryIndex: $categoryIndex",
              );
              print("Item ID: $itemId");
              print("Available categories: $categoryKeys");
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: MediaPlayer(mediaData: mediaDataForPlayer, baseUrl: baseUrl),
          ),
        );
      },
    );
  }
}
