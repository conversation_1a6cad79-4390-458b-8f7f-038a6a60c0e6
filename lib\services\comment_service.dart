import 'dart:convert';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient
import 'package:wicker/services/config_service.dart';

class CommentService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  // THE FIX: This method now accepts search and filter parameters
  Future<List<Map<String, dynamic>>> getComments({
    required String postId,
    String? searchQuery,
    String? sentiment,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      // Build the URI with query parameters
      final uri = Uri.parse('$baseUrl/api/comments/$postId').replace(
        queryParameters: {
          if (searchQuery != null && searchQuery.isNotEmpty)
            'search': searchQuery,
          if (sentiment != null && sentiment != 'all') 'sentiment': sentiment,
        },
      );

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load comments');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> postComment({
    required String postId,
    required String commentText,
    String? parentId,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/comments/create'),
        body: jsonEncode({
          'post_id': postId,
          'comment_text': commentText,
          'parent_id': parentId,
        }),
      );
      if (response.statusCode != 201) {
        throw Exception('Failed to post comment');
      }
    } catch (e) {
      rethrow;
    }
  }
}
