// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:wicker/services/places_service.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';
// import 'package:wicker/widgets/post_card.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/widgets/product_card.dart';

// class PlaceDetailScreen extends StatefulWidget {
//   final String placeId;

//   const PlaceDetailScreen({super.key, required this.placeId});

//   @override
//   State<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
// }

// class _PlaceDetailScreenState extends State<PlaceDetailScreen>
//     with SingleTickerProviderStateMixin {
//   final PlacesService _placesService = PlacesService();
//   final EcommerceService _ecommerceService = EcommerceService();
//   late Future<Map<String, dynamic>> _detailsFuture;
//   late TabController _tabController;

//   // --- NEW: Future to hold the product list ---
//   Future<List<Map<String, dynamic>>>? _productsFuture;
//   // --- End of NEW ---

//   @override
//   void initState() {
//     super.initState();
//     _detailsFuture = _placesService.getPlaceDetails(widget.placeId);
//     _tabController = TabController(length: 3, vsync: this);

//     // After getting place details, fetch the business and its products
//     _detailsFuture.then((placeData) {
//       final ownerId = placeData['created_by']?['\$oid'];
//       if (ownerId != null) {
//         _fetchBusinessProducts(ownerId);
//       }
//     });
//   }

//   /// Fetches the business for the place's owner, then fetches its products.
//   void _fetchBusinessProducts(String ownerId) {
//     setState(() {
//       _productsFuture = _ecommerceService.getBusinessByOwner(ownerId).then((
//         business,
//       ) {
//         if (business != null) {
//           final businessId = business['_id']['\$oid'];
//           return _ecommerceService.getBusinessProducts(businessId);
//         }
//         return []; // Return an empty list if no business is found
//       });
//     });
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }

//   Widget _buildNeubrutalistCard({
//     required Widget child,
//     Color? backgroundColor,
//     Color? borderColor,
//     double? borderWidth,
//     EdgeInsetsGeometry? padding,
//     EdgeInsetsGeometry? margin,
//   }) {
//     return Container(
//       margin: margin ?? const EdgeInsets.all(8),
//       padding: padding ?? const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: backgroundColor ?? Colors.white,
//         border: Border.all(
//           color: borderColor ?? Colors.black,
//           width: borderWidth ?? 3,
//         ),
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: borderColor ?? Colors.black,
//             offset: const Offset(6, 6),
//             blurRadius: 0,
//           ),
//         ],
//       ),
//       child: child,
//     );
//   }

//   Widget _buildInfoChip({
//     required String label,
//     required IconData icon,
//     Color? backgroundColor,
//     Color? textColor,
//   }) {
//     return _buildNeubrutalistCard(
//       backgroundColor: backgroundColor ?? const Color(0xFFFF6B6B),
//       borderColor: Colors.black,
//       padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
//       margin: const EdgeInsets.only(right: 8, bottom: 8),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(icon, size: 16, color: textColor ?? Colors.white),
//           const SizedBox(width: 4),
//           Text(
//             label,
//             style: TextStyle(
//               color: textColor ?? Colors.white,
//               fontWeight: FontWeight.bold,
//               fontSize: 12,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildGoodCard(Map<String, dynamic> good) {
//     Color typeColor;
//     IconData typeIcon;

//     switch (good['type']) {
//       case 'physical':
//         typeColor = const Color(0xFF4ECDC4);
//         typeIcon = Icons.shopping_bag;
//         break;
//       case 'digital':
//         typeColor = const Color(0xFFFFE66D);
//         typeIcon = Icons.cloud_download;
//         break;
//       case 'service':
//         typeColor = const Color(0xFFFF6B6B);
//         typeIcon = Icons.handyman;
//         break;
//       default:
//         typeColor = const Color(0xFF95E1D3);
//         typeIcon = Icons.category;
//     }

//     return _buildNeubrutalistCard(
//       backgroundColor: Colors.white,
//       margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             children: [
//               ClipRRect(
//                 borderRadius: BorderRadius.circular(12),
//                 child: Container(
//                   decoration: BoxDecoration(
//                     border: Border.all(color: Colors.black, width: 2),
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: Image.network(
//                     good['image'],
//                     width: 80,
//                     height: 80,
//                     fit: BoxFit.cover,
//                   ),
//                 ),
//               ),
//               const SizedBox(width: 16),
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       children: [
//                         Expanded(
//                           child: Text(
//                             good['name'],
//                             style: const TextStyle(
//                               fontSize: 18,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                         ),
//                         _buildInfoChip(
//                           label: good['type'].toUpperCase(),
//                           icon: typeIcon,
//                           backgroundColor: typeColor,
//                         ),
//                       ],
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       good['description'],
//                       style: TextStyle(fontSize: 14, color: Colors.grey[600]),
//                       maxLines: 2,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                     const SizedBox(height: 12),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Text(
//                           '\$${good['price'].toStringAsFixed(2)}',
//                           style: const TextStyle(
//                             fontSize: 20,
//                             fontWeight: FontWeight.bold,
//                             color: Color(0xFF6C5CE7),
//                           ),
//                         ),
//                         Container(
//                           padding: const EdgeInsets.symmetric(
//                             horizontal: 16,
//                             vertical: 8,
//                           ),
//                           decoration: BoxDecoration(
//                             color: good['inStock']
//                                 ? const Color(0xFF00D2D3)
//                                 : Colors.grey[300],
//                             border: Border.all(color: Colors.black, width: 2),
//                             borderRadius: BorderRadius.circular(20),
//                             boxShadow: [
//                               BoxShadow(
//                                 color: Colors.black,
//                                 offset: const Offset(2, 2),
//                                 blurRadius: 0,
//                               ),
//                             ],
//                           ),
//                           child: Text(
//                             good['inStock'] ? 'BUY NOW' : 'SOLD OUT',
//                             style: TextStyle(
//                               color: good['inStock']
//                                   ? Colors.white
//                                   : Colors.grey[600],
//                               fontWeight: FontWeight.bold,
//                               fontSize: 12,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       body: FutureBuilder<Map<String, dynamic>>(
//         future: _detailsFuture,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return Center(
//               child: _buildNeubrutalistCard(
//                 backgroundColor: const Color(0xFFFF6B6B),
//                 child: const CircularProgressIndicator(
//                   valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                 ),
//               ),
//             );
//           }
//           if (snapshot.hasError || !snapshot.hasData) {
//             return Center(
//               child: _buildNeubrutalistCard(
//                 backgroundColor: const Color(0xFFFF6B6B),
//                 child: const Text(
//                   'Could not load place details.',
//                   style: TextStyle(
//                     color: Colors.white,
//                     fontWeight: FontWeight.bold,
//                     fontSize: 16,
//                   ),
//                 ),
//               ),
//             );
//           }

//           final place = snapshot.data!;
//           final posts = List<Map<String, dynamic>>.from(
//             place['tagged_posts'] ?? [],
//           );

//           bool hasImage = false;
//           String imageUrl = '';

//           if (place['photos'] != null && (place['photos'] as List).isNotEmpty) {
//             final String baseUrl =
//                 defaultTargetPlatform == TargetPlatform.android
//                 ? "http://192.168.8.107:5000"
//                 : "http://127.0.0.1:5000";
//             String imagePath = place['photos'][0];
//             String correctedPath = imagePath.replaceAll('\\', '/');
//             imageUrl = '$baseUrl/$correctedPath';
//             hasImage = true;
//           }

//           return NestedScrollView(
//             headerSliverBuilder: (context, innerBoxIsScrolled) {
//               return [
//                 SliverAppBar(
//                   expandedHeight: 250.0,
//                   floating: false,
//                   pinned: true,
//                   backgroundColor: const Color(0xFF6C5CE7),
//                   leading: IconButton(
//                     icon: Container(
//                       padding: const EdgeInsets.all(4),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         border: Border.all(color: Colors.black, width: 2),
//                         borderRadius: BorderRadius.circular(8),
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black,
//                             offset: const Offset(2, 2),
//                             blurRadius: 0,
//                           ),
//                         ],
//                       ),
//                       child: const Icon(Icons.arrow_back, color: Colors.black),
//                     ),
//                     onPressed: () => Navigator.of(context).pop(),
//                   ),
//                   flexibleSpace: FlexibleSpaceBar(
//                     title: Container(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 8,
//                         vertical: 4,
//                       ),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         border: Border.all(color: Colors.black, width: 2),
//                         borderRadius: BorderRadius.circular(8),
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.black,
//                             offset: const Offset(2, 2),
//                             blurRadius: 0,
//                           ),
//                         ],
//                       ),
//                       child: Text(
//                         place['name'],
//                         style: const TextStyle(
//                           color: Colors.black,
//                           fontWeight: FontWeight.bold,
//                           fontSize: 16,
//                         ),
//                       ),
//                     ),
//                     background: Stack(
//                       fit: StackFit.expand,
//                       children: [
//                         Container(
//                           decoration: BoxDecoration(
//                             border: Border.all(color: Colors.black, width: 4),
//                           ),
//                           child: hasImage
//                               ? Image.network(imageUrl, fit: BoxFit.cover)
//                               : Image.network(
//                                   'https://picsum.photos/seed/${place['_id']['\$oid']}/800/400',
//                                   fit: BoxFit.cover,
//                                 ),
//                         ),
//                         Container(
//                           decoration: BoxDecoration(
//                             gradient: LinearGradient(
//                               begin: Alignment.topCenter,
//                               end: Alignment.bottomCenter,
//                               colors: [
//                                 Colors.transparent,
//                                 Colors.black.withOpacity(0.3),
//                               ],
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ];
//             },
//             body: Column(
//               children: [
//                 Container(
//                   margin: const EdgeInsets.all(16),
//                   decoration: BoxDecoration(
//                     color: Colors.white,
//                     border: Border.all(color: Colors.black, width: 3),
//                     borderRadius: BorderRadius.circular(16),
//                     boxShadow: [
//                       BoxShadow(
//                         color: Colors.black,
//                         offset: const Offset(4, 4),
//                         blurRadius: 0,
//                       ),
//                     ],
//                   ),
//                   child: TabBar(
//                     controller: _tabController,
//                     indicatorColor: const Color(0xFFFF6B6B),
//                     indicatorWeight: 4,
//                     labelColor: Colors.black,
//                     unselectedLabelColor: Colors.grey,
//                     labelStyle: const TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 16,
//                     ),
//                     tabs: const [
//                       Tab(icon: Icon(Icons.info_outline), text: 'ABOUT'),
//                       Tab(icon: Icon(Icons.post_add), text: 'POSTS'),
//                       Tab(icon: Icon(Icons.shopping_cart), text: 'SHOP'),
//                     ],
//                   ),
//                 ),
//                 Expanded(
//                   child: TabBarView(
//                     controller: _tabController,
//                     children: [
//                       // "About" Tab
//                       ListView(
//                         padding: const EdgeInsets.all(16),

//                         children: [
//                           _buildAboutTab(place),
//                           _buildPostsTab(posts),
//                           // --- THE FIX: The Shop tab now uses a FutureBuilder ---
//                           _buildShopTab(),
//                           // --- End of FIX ---
//                         ],
//                       ),
//                       // "Posts" Tab
//                       posts.isEmpty
//                           ? Center(
//                               child: _buildNeubrutalistCard(
//                                 backgroundColor: const Color(0xFFFFE66D),
//                                 child: Column(
//                                   mainAxisSize: MainAxisSize.min,
//                                   children: [
//                                     Icon(
//                                       Icons.photo_library_outlined,
//                                       size: 48,
//                                       color: Colors.grey[600],
//                                     ),
//                                     const SizedBox(height: 16),
//                                     const Text(
//                                       'NO POSTS YET',
//                                       style: TextStyle(
//                                         fontSize: 18,
//                                         fontWeight: FontWeight.bold,
//                                       ),
//                                     ),
//                                     const SizedBox(height: 8),
//                                     Text(
//                                       'Be the first to share a post at this place!',
//                                       style: TextStyle(color: Colors.grey[600]),
//                                       textAlign: TextAlign.center,
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             )
//                           : ListView.builder(
//                               padding: const EdgeInsets.symmetric(vertical: 8),
//                               itemCount: posts.length,
//                               itemBuilder: (context, index) {
//                                 return Container(
//                                   margin: const EdgeInsets.symmetric(
//                                     horizontal: 16,
//                                     vertical: 8,
//                                   ),
//                                   child: PostCard(postData: posts[index]),
//                                 );
//                               },
//                             ),
//                       // "Shop" Tab
//                       ListView(
//                         padding: const EdgeInsets.symmetric(vertical: 8),
//                         children: [
//                           Padding(
//                             padding: const EdgeInsets.all(16),
//                             child: _buildNeubrutalistCard(
//                               backgroundColor: const Color(0xFF6C5CE7),
//                               child: Row(
//                                 children: [
//                                   const Icon(
//                                     Icons.storefront,
//                                     color: Colors.white,
//                                     size: 32,
//                                   ),
//                                   const SizedBox(width: 16),
//                                   const Expanded(
//                                     child: Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         Text(
//                                           'SHOP LOCAL',
//                                           style: TextStyle(
//                                             fontSize: 20,
//                                             fontWeight: FontWeight.bold,
//                                             color: Colors.white,
//                                           ),
//                                         ),
//                                         Text(
//                                           'Support this amazing place!',
//                                           style: TextStyle(
//                                             color: Colors.white,
//                                             fontSize: 14,
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                           ..._dummyGoods.map((good) => _buildGoodCard(good)),
//                           const SizedBox(height: 20),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }

//   /// Builds the shop tab using real data from the backend.
//   Widget _buildShopTab() {
//     if (_productsFuture == null) {
//       return const Center(child: Text("This user hasn't set up a shop yet."));
//     }

//     return FutureBuilder<List<Map<String, dynamic>>>(
//       future: _productsFuture,
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const Center(child: CircularProgressIndicator());
//         }
//         if (snapshot.hasError) {
//           return Center(
//             child: Text("Error loading products: ${snapshot.error}"),
//           );
//         }
//         if (!snapshot.hasData || snapshot.data!.isEmpty) {
//           return Center(
//             child: NeuCard(
//               backgroundColor: const Color(0xFFFFE66D),
//               child: const Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Icon(Icons.storefront, size: 48),
//                   SizedBox(height: 16),
//                   Text(
//                     'NO PRODUCTS YET',
//                     style: TextStyle(fontWeight: FontWeight.bold),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }

//         final products = snapshot.data!;
//         return ListView.builder(
//           padding: const EdgeInsets.symmetric(vertical: 8),
//           itemCount: products.length,
//           itemBuilder: (context, index) {
//             // Use the reusable ProductCard widget
//             return ProductCard(productData: products[index]);
//           },
//         );
//       },
//     );
//   }
// }
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/product_card.dart';

class PlaceDetailScreen extends StatefulWidget {
  final String placeId;

  const PlaceDetailScreen({super.key, required this.placeId});

  @override
  State<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
}

class _PlaceDetailScreenState extends State<PlaceDetailScreen>
    with SingleTickerProviderStateMixin {
  final PlacesService _placesService = PlacesService();
  final EcommerceService _ecommerceService = EcommerceService();
  final ConfigService _configService = ConfigService.instance;

  late Future<Map<String, dynamic>> _detailsFuture;
  late Future<String> _baseUrlFuture;
  late TabController _tabController;
  Future<List<Map<String, dynamic>>>? _productsFuture;

  @override
  void initState() {
    super.initState();
    _detailsFuture = _placesService.getPlaceDetails(widget.placeId);
    _baseUrlFuture = _configService.getBaseUrl();
    _tabController = TabController(length: 3, vsync: this);

    // After getting place details, fetch the business and its products
    _detailsFuture.then((placeData) {
      final ownerId = placeData['created_by']?['\$oid'];
      if (ownerId != null) {
        _fetchBusinessProducts(ownerId);
      }
    });
  }

  /// Fetches the business for the place's owner, then fetches its products.
  void _fetchBusinessProducts(String ownerId) {
    setState(() {
      _productsFuture = _ecommerceService.getBusinessByOwner(ownerId).then((
        business,
      ) {
        if (business != null) {
          final businessId = business['_id']['\$oid'];
          return _ecommerceService.getBusinessProducts(businessId);
        }
        return []; // Return an empty list if no business is found
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: FutureBuilder<List<dynamic>>(
        future: Future.wait([_detailsFuture, _baseUrlFuture]),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: NeuCard(
                backgroundColor: const Color(0xFFFF6B6B),
                child: Text(
                  'Error: ${snapshot.error ?? "Could not load details."}',
                ),
              ),
            );
          }

          final place = snapshot.data![0] as Map<String, dynamic>;
          final baseUrl = snapshot.data![1] as String;
          final posts = List<Map<String, dynamic>>.from(
            place['tagged_posts'] ?? [],
          );

          bool hasImage =
              place['photos'] != null && (place['photos'] as List).isNotEmpty;
          String imageUrl = '';

          if (hasImage) {
            String imagePath = place['photos'][0];
            imageUrl = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
          } else {
            imageUrl =
                'https://picsum.photos/seed/${place['_id']['\$oid']}/800/400';
          }

          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: 250.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: const Color(0xFF6C5CE7),
                  leading: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const NeuCard(
                        margin: EdgeInsets.zero,
                        padding: EdgeInsets.zero,
                        child: Center(
                          child: Icon(Icons.arrow_back, color: Colors.black),
                        ),
                      ),
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    centerTitle: true,
                    titlePadding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 48,
                    ),
                    title: NeuCard(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      margin: EdgeInsets.zero,
                      child: Text(
                        place['name'],
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.network(imageUrl, fit: BoxFit.cover),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.4),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ];
            },
            body: Column(
              children: [
                NeuCard(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  padding: EdgeInsets.zero,
                  child: TabBar(
                    controller: _tabController,
                    indicator: const BoxDecoration(
                      color: Color(0xFFFFE66D),
                      border: Border(
                        bottom: BorderSide(color: Colors.black, width: 3.0),
                      ),
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.grey.shade700,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    tabs: const [
                      Tab(icon: Icon(Icons.info_outline), text: 'About'),
                      Tab(icon: Icon(Icons.post_add), text: 'Posts'),
                      Tab(
                        icon: Icon(Icons.shopping_cart_outlined),
                        text: 'Shop',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAboutTab(place),
                      _buildPostsTab(posts),
                      _buildShopTab(),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Builds the "About" tab with dynamic data from the 'place' object.
  Widget _buildAboutTab(Map<String, dynamic> place) {
      // Check if the current user is the creator and if the place is unclaimed
  final currentUserId = getJwtIdentity(); // You'll need a way to get this
  final creatorId = place['created_by']['\$oid'];
  final bool canClaim = (currentUserId == creatorId && place['business_id'] == null);

    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        NeuCard(
          backgroundColor: const Color(0xFF4ECDC4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'PLACE INFO',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                children: [
                  NeuChip(
                    label: place['category'] ?? 'Unknown',
                    icon: Icons.category,
                    backgroundColor: const Color(0xFFFFE66D),
                    textColor: Colors.black,
                  ),
                  // These can be replaced with real data later
                  const NeuChip(
                    label: 'VERIFIED',
                    icon: Icons.verified,
                    backgroundColor: Color(0xFF00D2D3),
                  ),
                  const NeuChip(
                    label: 'POPULAR',
                    icon: Icons.trending_up,
                    backgroundColor: Color(0xFFFF6B6B),
                  ),
                ],
              ),
            ],
          ),
        ),
        NeuCard(
          backgroundColor: const Color(0xFF95E1D3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.star, color: Colors.orange[800]),
                  const SizedBox(width: 8),
                  const Text(
                    'REVIEWS',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    (place['overall_rating'] ?? 0.0).toStringAsFixed(1),
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: List.generate(
                          5,
                          (index) => Icon(
                            Icons.star,
                            color: index < (place['overall_rating'] ?? 0.0)
                                ? Colors.orange
                                : Colors.grey[400],
                            size: 16,
                          ),
                        ),
                      ),
                      Text(
                        'Based on ${(place['reviews'] as List?)?.length ?? 0} reviews',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
           // --- NEW: Conditional "Claim Place" Button ---
      if (canClaim)
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: GestureDetector(
            onTap: () async {
              try {
                final message = await _placesService.claimPlace(widget.placeId);
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message)));
                // Refresh the screen to hide the button
                setState(() => _detailsFuture = _placesService.getPlaceDetails(widget.placeId));
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(e.toString())));
              }
            },
            child: NeuCard(
              backgroundColor: const Color(0xFF00D2D3),
              child: const Center(
                child: Text(
                  'Claim this Place for Your Business',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the "Posts" tab with real data from the 'posts' list.
  Widget _buildPostsTab(List<Map<String, dynamic>> posts) {
    if (posts.isEmpty) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 48,
                color: Colors.grey[800],
              ),
              const SizedBox(height: 16),
              const Text(
                'NO POSTS YET',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Be the first to share a post here!',
                style: TextStyle(color: Colors.grey[800]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        return PostCard(postData: posts[index]);
      },
    );
  }

  /// Builds the "Shop" tab using a FutureBuilder for real product data.
  Widget _buildShopTab() {
    if (_productsFuture == null) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.storefront, size: 48, color: Colors.grey[800]),
              const SizedBox(height: 16),
              const Text(
                'NO SHOP AVAILABLE',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      );
    }

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _productsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Text("Error loading products: ${snapshot.error}"),
          );
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: NeuCard(
              backgroundColor: const Color(0xFFFFE66D),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.storefront, size: 48),
                  SizedBox(height: 16),
                  Text(
                    'NO PRODUCTS YET',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          );
        }

        final products = snapshot.data!;
        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: products.length,
          itemBuilder: (context, index) {
            return ProductCard(productData: products[index]);
          },
        );
      },
    );
  }
}
